<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <!-- One viewport meta tag is enough -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Shans System - Invoice</title>
    <style>
        /* Basic Reset */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        /* Body Styling */
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f4f4f4;
            padding: 20px;
        }

        /* Container */
        .container {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }

        /* Header */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
        }

        .logo {
            width: 150px;
            height: auto;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .logo img {
            max-width: 100%;
            max-height: 100px;
            object-fit: contain;
            width: auto;
            height: auto;
        }

        h1 {
            text-align: center;
            flex-grow: 1;
            margin: 0 20px;
            font-size: 32px;
        }

        /* Info Container for mobile layout */
        .info-container {
            display: flex;
            flex-direction: column;
            width: 100%;
        }

        /* Company Details */
        .company-details {
            margin-bottom: 20px;
        }

        .company-details h2 {
            margin-bottom: 10px;
            font-size: 24px;
        }

        .address-contact {
            margin-bottom: 10px;
        }

        /* Billing and Shipping */
        .billing-shipping {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .billing,
        .shipping {
            width: 48%;
            margin-bottom: 20px;
        }

        .billing h3,
        .shipping h3 {
            margin-bottom: 10px;
            font-size: 20px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }

        /* Div-based Table Styling */
        .table-container {
            width: 100%;
            margin-bottom: 20px;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
            overflow-x: auto; /* Allow horizontal scrolling on desktop */
        }

        .div-table {
            display: flex;
            flex-direction: column;
            width: 100%;
            background-color: white;
            font-size: 16px; /* Increased from 14px to 16px (+2px) */
            border: 1px solid #e0e0e0;
            margin-bottom: 0;
        }

        .div-table-row {
            display: flex;
            flex-direction: row;
            border-bottom: 1px solid #e0e0e0;
        }

        .div-table-row:nth-child(even) {
            background-color: #f9f9f9;
        }

        .div-table-row:hover {
            background-color: #f0f7ff;
        }

        .div-table-header {
            background-color: #f2f2f2;
            font-weight: bold;
            border-bottom: 2px solid #d0d0d0;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .div-table-cell {
            padding: 8px 10px;
            border-right: 1px solid #e0e0e0;
            white-space: nowrap; /* No wrapping on desktop */
            overflow: hidden;
            text-overflow: ellipsis;
            display: flex;
            align-items: center;
        }

        .div-table-cell:last-child {
            border-right: none;
        }

        .div-table-heading {
            color: #333;
            font-weight: 700; /* Consistent bold weight */
        }

        /* Column-specific styling */
        .product-column {
            flex: 4;
            min-width: 250px;
            max-width: 350px;
            justify-content: flex-start;
            padding-left: 12px;
        }

        .qty-column {
            flex: 0.3;
            min-width: 30px;
            max-width: 40px;
            justify-content: center;
            font-weight: 700; /* Make it bold */
            text-align: center;
            font-size: 15px; /* Increased from 13px to 15px (+2px) */
        }

        .price-column {
            flex: 0.7;
            min-width: 70px;
            justify-content: flex-end;
            padding-right: 12px;
            font-size: 15px; /* Increased from 13px to 15px (+2px) */
        }

        /* For the tax column that may be hidden */
        .tax-column {
            display: flex; /* Will be toggled with JavaScript */
        }

        /* Div Table Body */
        .div-table-body {
            display: flex;
            flex-direction: column;
        }

        /* Additional Comments Section */
        .additional-comments {
            margin-bottom: 20px;
        }

        .additional-comments h3 {
            margin-bottom: 10px;
            font-size: 20px;
        }

        .additional-comments p {
            font-size: 16px;
            white-space: pre-wrap;
        }

        /* Terms Section */
        .terms {
            text-align: left;
            margin-bottom: 20px;
        }

        .terms h3 {
            margin-bottom: 5px;
            font-size: 16px;
        }

        .terms p {
            font-size: 14px;
        }

        /* Totals Section */
        .totals {
            margin-top: 30px;
            padding: 20px;
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .totals p {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 16px;
            margin-bottom: 10px;
        }

        .totals p span {
            text-align: right;
            font-weight: bold;
        }

        .totals p.tax {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 16px;
            margin-bottom: 10px;
        }

        .totals p.tax span {
            text-align: right;
            font-weight: bold;
        }

        /* Footer */
        .footer {
            text-align: center;
            color: #666;
            font-size: 14px;
            margin-top: 30px;
        }

        /* Button Styles */
        .button-container {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }

        .action-button {
            background-color: #4a90e2;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .action-button:hover {
            background-color: #357ABD;
        }

        .action-button:disabled {
            background-color: #aaa;
            cursor: not-allowed;
        }

        /* Loading Spinner Styles */
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 2s linear infinite;
            display: inline-block;
            vertical-align: middle;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Toast Container and Toast Styles */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .toast {
            background-color: #333;
            color: #fff;
            padding: 10px 15px;
            border-radius: 5px;
            opacity: 0.95;
            font-size: 14px;
            animation: fadeInOut 3s forwards;
        }

        .toast.success {
            background-color: #28a745;
        }

        .toast.error {
            background-color: #dc3545;
        }

        .toast.info {
            background-color: #007bff;
        }

        @keyframes fadeInOut {
            0% { opacity: 0; }
            10% { opacity: 0.95; }
            90% { opacity: 0.95; }
            100% { opacity: 0; }
        }

        /* Responsive Design */
        @media (max-width: 830px) {
            body {
                padding: 10px;
            }

            .container {
                padding: 15px;
            }

            /* Adjust table for medium screens */
            .div-table-cell {
                padding: 6px 8px;
                font-size: 15px; /* Increased from 13px to 15px (+2px) */
            }

            /* Adjust column widths for medium screens */
            .product-column {
                min-width: 180px;
                flex: 3;
            }

            .qty-column {
                min-width: 25px;
                flex: 0.3;
                font-size: 12px;
            }

            .price-column {
                min-width: 60px;
                flex: 0.6;
                font-size: 12px;
            }

            /* Adjust header for medium screens */
            h1 { font-size: 28px; }
            h2 { font-size: 22px; }
            h3 { font-size: 18px; }

            .logo {
                width: 80px;
            }

            .logo img {
                max-height: 80px;
            }

            /* Adjust billing/shipping for medium screens */
            .billing, .shipping {
                width: 100%;
                margin-bottom: 15px;
            }

            .billing-shipping {
                flex-direction: column;
            }
        }

        /* Specific adjustments for small screens */
        @media (max-width: 650px) {
            body {
                padding: 5px;
            }

            .container {
                padding: 12px;
            }

            /* Adjust header for small screens */
            h1 { font-size: 24px; }
            h2 { font-size: 20px; }
            h3 { font-size: 16px; }

            .logo {
                width: 60px;
            }

            .logo img {
                max-height: 60px;
            }

            /* Make the date smaller on mobile */
            #invoice-date {
                font-size: 12px;
            }

            /* Adjust info layout for small screens */
            .info-container {
                flex-direction: row;
                justify-content: space-between;
                align-items: flex-start;
                gap: 10px;
            }

            .company-details {
                flex: 1;
                font-size: 12px;
                padding-right: 10px;
            }

            .company-details h2 {
                font-size: 16px;
                margin-bottom: 5px;
            }

            .address-contact p {
                font-size: 11px;
                line-height: 1.3;
            }

            .billing-shipping {
                flex: 1;
                flex-direction: column;
                font-size: 12px;
            }

            .billing, .shipping {
                width: 100%;
                margin-bottom: 10px;
                font-size: 11px;
            }

            .billing h3, .shipping h3 {
                font-size: 14px;
                margin-bottom: 5px;
            }

            .billing p, .shipping p {
                line-height: 1.3;
            }

            /* Adjust table for small screens */
            .table-container {
                overflow-x: visible; /* Disable horizontal scrolling on mobile */
                box-shadow: none;
                border-radius: 0;
            }

            .div-table {
                border-radius: 4px;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            }

            .div-table-cell {
                padding: 5px 6px;
                font-size: 13px; /* Increased from 11px to 13px (+2px) */
            }

            .product-column {
                min-width: 0; /* Remove min-width to allow full flexibility */
                flex: 2;
            }

            .qty-column {
                min-width: 0; /* Remove min-width to allow full flexibility */
                max-width: none;
                flex: 0.25;
                font-size: 13px; /* Increased from 11px to 13px (+2px) */
            }

            .price-column {
                min-width: 0; /* Remove min-width to allow full flexibility */
                flex: 0.5;
                font-size: 13px; /* Increased from 11px to 13px (+2px) */
            }
        }

        /* Specific adjustments for very small screens */
        @media (max-width: 480px) {
            body {
                padding: 0;
            }

            .container {
                padding: 10px;
            }

            /* Make the date even smaller on very small screens */
            #invoice-date {
                font-size: 10px;
            }

            /* Further reduce text size for very small screens */
            .company-details {
                font-size: 10px;
            }

            .company-details h2 {
                font-size: 14px;
            }

            .address-contact p {
                font-size: 9px;
            }

            .billing, .shipping {
                font-size: 10px;
            }

            .billing h3, .shipping h3 {
                font-size: 12px;
            }

            .billing p, .shipping p {
                font-size: 9px;
            }

            /* Make all table headers consistent size on mobile */
            .div-table-heading {
                font-size: 11px !important; /* Override inline styles */
            }

            /* Mobile-optimized table */
            .div-table-cell {
                padding: 4px 5px;
                font-size: 13px; /* Increased from 11px to 13px (+2px) */
                white-space: normal; /* Allow text wrapping on mobile */
            }

            /* Adjust column proportions for mobile */
            .product-column {
                flex: 1.5;
            }

            .qty-column {
                flex: 0.2;
                font-size: 12px; /* Increased from 10px to 12px (+2px) */
            }

            .price-column {
                flex: 0.4;
                font-size: 12px; /* Increased from 10px to 12px (+2px) */
            }
        }

        /* Ultra small screens */
        @media (max-width: 320px) {
            .container {
                padding: 5px;
            }

            .div-table-cell {
                padding: 2px 3px;
                font-size: 11px; /* Increased from 9px to 11px (+2px) */
            }

            /* Even smaller headers for very small screens */
            .div-table-heading {
                font-size: 10px !important; /* Override inline styles */
            }

            /* Make the date tiny on ultra small screens */
            #invoice-date {
                font-size: 9px;
            }

            /* Extreme size reduction for ultra small screens */
            .company-details {
                font-size: 9px;
            }

            .company-details h2 {
                font-size: 12px;
            }

            .address-contact p {
                font-size: 8px;
            }

            .billing, .shipping {
                font-size: 9px;
            }

            .billing h3, .shipping h3 {
                font-size: 11px;
            }

            .billing p, .shipping p {
                font-size: 8px;
            }

            /* Add a bit more spacing between the two sections */
            .info-container {
                gap: 5px;
            }
        }
    </style>
</head>
<body>
    <!-- Toast Container -->
    <div class="toast-container" id="toastContainer"></div>

    <div style="display: flex; margin: 20px;">
        <a href="index.html" style="text-decoration: none; color: #007BFF; font-size: 16px; margin-right: 20px; display: inline-block;">&larr; Back to Home</a>
        <a href="javascript:void(0)" id="edit-order-button" style="text-decoration: none; color: #007BFF; font-size: 16px; display: inline-block;">✏️ Edit Order</a>
    </div>

    <div id="pdf-1" class="container">
        <div id="invoice">
            <div class="header">
                <p id="invoice-date">Date: <!-- Dynamic Date --></p>
                <h1>INVOICE</h1>
                <div class="logo">
                    <img src="./Assets/logo2.png" alt="Company Logo">
                </div>
            </div>

            <div class="info-container">
                <div class="company-details">
                    <h2 id="company-name">Shans Accessories PTY LTD</h2>
                    <p>61 Civin Drive<br>Bedfordview<br>Johannesburg</p>
                    <div class="address-contact">
                        <p id="banking-info"></p>
                    </div>
                </div>

                <div class="billing-shipping">
                    <div class="billing">
                        <h3>BILL TO</h3>
                        <p><!-- Customer Billing Information --></p>
                    </div>
                    <div class="shipping">
                        <h3>SHIP TO</h3>
                        <p><!-- Customer Shipping Information --></p>
                    </div>
                </div>
            </div>

            <!-- Products Table (Div-based) -->
            <div class="table-container">
                <div class="div-table">
                    <!-- Table Header -->
                    <div class="div-table-row div-table-header" id="table-header">
                        <div class="div-table-cell div-table-heading product-column">Product</div>
                        <div class="div-table-cell div-table-heading qty-column" style="font-weight: 700; text-align: center;">QTY</div>
                        <div class="div-table-cell div-table-heading price-column">PRICE</div>
                        <div class="div-table-cell div-table-heading price-column tax-column">TAX</div>
                        <div class="div-table-cell div-table-heading price-column">TOTAL</div>
                    </div>
                    <!-- Table Body - Dynamic Product Rows Will Be Inserted Here -->
                    <div class="div-table-body">
                        <!-- JavaScript will populate this section -->
                    </div>
                </div>
            </div>

            <!-- Additional Comments Section -->
            <div id="additional-comments" class="additional-comments" style="margin-bottom: 20px;">
                <!-- Additional Comments will be inserted here if available -->
            </div>

            <!-- Terms Section -->
            <div class="terms">
                <h3>Terms</h3>
                <p>No refunds or exchanges on correctly supplied items</p>
            </div>

            <!-- Totals Section -->
            <div class="totals">
                <p class="subtotal">Product Amount (Subtotal): <span id="subtotalAmount">R0</span></p>
                <p class="tax" id="tax-info" style="display: none;">Tax (15%): <span id="taxAmount">R0</span></p>
                <p class="total">Total Amount: <span id="totalAmount">R0</span></p>
            </div>
        </div>

        <div class="footer">
            <p>Follow us to see more on</p>
            <p>Instagram: @shans_car_accessories</p>
            <p>Twitter/Pinterest: Shans Accessories</p>
            <p>Facebook: Shans Accessories (By Car Brand)</p>
            <p>YouTube/Tik Tok: Shans Accessories All products are Non OEM</p>
        </div>
    </div>

    <!-- Buttons Container -->
    <div class="button-container">
        <button id="confirm-button" class="action-button">Confirm</button>
        <button id="screenshot-button" class="action-button">Download Image</button>
        <div id="loading-spinner" class="spinner" style="display: none;"></div>
    </div>

    <div id="pdf-preview"></div>

    <!-- Include html2pdf.js (optional) -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
    <!-- Include html2canvas for image capturing -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

    <script>
        /**
         * Display a non-blocking toast message in the top-right corner.
         * @param {string} message - The message to display.
         * @param {string} [type='info'] - The type of toast ('success', 'error', 'info').
         */
        function showToastMessage(message, type = 'info') {
            const toastContainer = document.getElementById('toastContainer');
            const toast = document.createElement('div');
            toast.classList.add('toast', type);
            toast.textContent = message;
            toastContainer.appendChild(toast);
            setTimeout(() => {
                toast.remove();
            }, 3000);
        }

        // Retrieve a cookie by name
        function getCookie(name) {
            const cname = name + "=";
            const decodedCookie = decodeURIComponent(document.cookie);
            const ca = decodedCookie.split(';');
            for(let i = 0; i < ca.length; i++) {
                let c = ca[i].trim();
                if (c.indexOf(cname) === 0) {
                    return c.substring(cname.length, c.length);
                }
            }
            return "";
        }

        // Function to set a cookie
        function setCookie(name, value, days = 1) {
            const date = new Date();
            date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
            const expires = `expires=${date.toUTCString()}`;
            document.cookie = `${name}=${value};${expires};path=/`;
        }

        // Format currency in ZAR, no decimals
        function formatCurrency(amount) {
            return 'R' + parseFloat(amount).toLocaleString('en-ZA', {
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            });
        }

        // Sanitize HTML to prevent XSS
        function sanitizeHTML(str) {
            const temp = document.createElement('div');
            temp.textContent = str;
            return temp.innerHTML;
        }

        // Format date
        function formatDate(date) {
            const options = { year: 'numeric', month: 'short', day: 'numeric' };
            return new Intl.DateTimeFormat('en-US', options).format(date);
        }

        // Generate a random 7-digit reference number (if needed)
        function generateReferenceNumber() {
            return Math.floor(1000000 + Math.random() * 9000000).toString();
        }

        /**
         * Populate the invoice with data from cookies, applying the same
         * "tax = 15% of final price" rule as the receipt page:
         *   - If tax is added, net = 85% of product.price, tax = 15% of product.price
         *   - If no tax, net = product.price, tax = 0
         */
        function populateInvoice() {
            // Clear the invoiceConfirmed cookie when the page loads
            // This ensures we can create a new invoice each time
            setCookie('invoiceConfirmed', '', -1); // Expire the cookie

            const customerInfoCookie = getCookie('customerInfo');
            const selectedProductsCookie = getCookie('selectedProducts');
            const subtotalAmountCookie = getCookie('subtotalAmount');
            const taxAmountCookie = getCookie('taxAmount');
            const totalAmountCookie = getCookie('totalAmount');
            const selectedCompanyCookie = getCookie('selectedCompany');

            if (!customerInfoCookie ||
                !selectedProductsCookie ||
                !subtotalAmountCookie ||
                !taxAmountCookie ||
                !totalAmountCookie ||
                !selectedCompanyCookie) {
                showToastMessage('No invoice data found. Please generate an invoice first.', 'error');
                window.location.href = 'index.html';
                return;
            }

            const customerInfo = JSON.parse(customerInfoCookie);
            const selectedProducts = JSON.parse(selectedProductsCookie);
            const subtotalAmount = parseFloat(JSON.parse(subtotalAmountCookie));
            const taxAmount = parseFloat(JSON.parse(taxAmountCookie));
            const totalAmount = parseFloat(JSON.parse(totalAmountCookie));
            const selectedCompany = JSON.parse(selectedCompanyCookie);

            // Populate Company Info
            document.getElementById('company-name').textContent = selectedCompany.name;
            document.getElementById('banking-info').innerHTML = selectedCompany.bankingInformation;

            // Billing
            const billingElement = document.querySelector('.billing p');
            billingElement.innerHTML = `
                <strong>${sanitizeHTML(customerInfo.billing.name)}</strong><br>
                ${sanitizeHTML(customerInfo.billing.address)}<br>
                Email: ${sanitizeHTML(customerInfo.billing.email)}<br>
                Phone: ${sanitizeHTML(customerInfo.billing.phone)}
            `;

            // Shipping
            const shippingElement = document.querySelector('.shipping p');
            if (customerInfo.shipping) {
                shippingElement.innerHTML = `
                    <strong>${sanitizeHTML(customerInfo.shipping.name)}</strong><br>
                    ${sanitizeHTML(customerInfo.shipping.address)}<br>
                    Email: ${sanitizeHTML(customerInfo.shipping.email)}<br>
                    Phone: ${sanitizeHTML(customerInfo.shipping.phone)}
                `;
            } else {
                shippingElement.innerHTML = `Same as billing address`;
            }

            // Determine if tax is actually added by checking if taxAmount > 0
            const isTaxAdded = (taxAmount > 0);
            const taxInfo = document.getElementById('tax-info');

            // If there's any tax, show the "TAX" column, otherwise hide it
            const taxColumn = document.querySelector('.tax-column');
            if (isTaxAdded) {
                // Show the tax column
                taxColumn.style.display = 'flex';
                taxInfo.style.display = 'flex';
            } else {
                // Hide the tax column
                taxColumn.style.display = 'none';
                taxInfo.style.display = 'none';
            }

            // Now populate the table rows
            const tableBody = document.querySelector('.div-table-body');
            tableBody.innerHTML = '';

            // Show a message if no products are selected
            if (selectedProducts.length === 0) {
                const emptyRow = document.createElement('div');
                emptyRow.className = 'div-table-row';
                emptyRow.style.justifyContent = 'center';
                emptyRow.style.padding = '20px';
                emptyRow.style.color = '#666';
                emptyRow.innerHTML = '<i>No products selected.</i>';
                tableBody.appendChild(emptyRow);
                return;
            }

            selectedProducts.forEach(product => {
                // Create a new row
                const row = document.createElement('div');
                row.className = 'div-table-row';

                // Calculate prices
                const netPrice = isTaxAdded ? product.price * 0.85 : product.price;
                const taxPerUnit = isTaxAdded ? product.price * 0.15 : 0;
                const lineTotal = product.price * product.quantity;

                // Product name cell
                const nameCell = document.createElement('div');
                nameCell.className = 'div-table-cell product-column';
                nameCell.textContent = product.name;
                row.appendChild(nameCell);

                // Quantity cell
                const qtyCell = document.createElement('div');
                qtyCell.className = 'div-table-cell qty-column';
                qtyCell.textContent = product.quantity;
                qtyCell.style.fontWeight = '700'; // Make QTY bold
                qtyCell.style.textAlign = 'center';
                row.appendChild(qtyCell);

                if (isTaxAdded) {
                    // Unit Price cell
                    const unitPriceCell = document.createElement('div');
                    unitPriceCell.className = 'div-table-cell price-column';
                    unitPriceCell.textContent = formatCurrency(netPrice.toFixed(0));
                    row.appendChild(unitPriceCell);

                    // Tax cell
                    const taxCell = document.createElement('div');
                    taxCell.className = 'div-table-cell price-column tax-column';
                    taxCell.textContent = formatCurrency(taxPerUnit.toFixed(0));
                    row.appendChild(taxCell);

                    // Total cell
                    const totalCell = document.createElement('div');
                    totalCell.className = 'div-table-cell price-column';
                    totalCell.textContent = formatCurrency(lineTotal.toFixed(0));
                    row.appendChild(totalCell);
                } else {
                    // Unit Price cell
                    const unitPriceCell = document.createElement('div');
                    unitPriceCell.className = 'div-table-cell price-column';
                    unitPriceCell.textContent = formatCurrency(product.price.toFixed(0));
                    row.appendChild(unitPriceCell);

                    // Total cell
                    const totalCell = document.createElement('div');
                    totalCell.className = 'div-table-cell price-column';
                    totalCell.textContent = formatCurrency(lineTotal.toFixed(0));
                    row.appendChild(totalCell);
                }

                // Add the row to the table body
                tableBody.appendChild(row);
            });

            // Additional Comments
            const comments = customerInfo.comments;
            const commentsDiv = document.getElementById('additional-comments');
            if (comments && comments.trim() !== "") {
                commentsDiv.innerHTML = `
                    <h3>Additional Comments:</h3>
                    <p>${sanitizeHTML(comments).replace(/\n/g, '<br>')}</p>
                `;
                commentsDiv.style.display = 'block';
            } else {
                commentsDiv.style.display = 'none';
            }

            // Totals
            document.getElementById('subtotalAmount').textContent = formatCurrency(subtotalAmount.toFixed(0));
            document.getElementById('taxAmount').textContent = formatCurrency(taxAmount.toFixed(0));
            document.getElementById('totalAmount').textContent = formatCurrency(totalAmount.toFixed(0));

            // Date
            const dateElement = document.getElementById('invoice-date');
            dateElement.textContent = 'Date: ' + formatDate(new Date());
        }

        // Confirm and send the invoice
        async function confirmInvoice() {
            const confirmButton = document.getElementById('confirm-button');
            const loadingSpinner = document.getElementById('loading-spinner');
            const pdfPreview = document.getElementById('pdf-preview');

            confirmButton.disabled = true;
            loadingSpinner.style.display = 'inline-block';
            pdfPreview.innerHTML = '';

            try {
                const customerInfoCookie = getCookie('customerInfo');
                const selectedProductsCookie = getCookie('selectedProducts');
                const subtotalAmountCookie = getCookie('subtotalAmount');
                const taxAmountCookie = getCookie('taxAmount');
                const totalAmountCookie = getCookie('totalAmount');
                const selectedCompanyCookie = getCookie('selectedCompany');

                if (!customerInfoCookie ||
                    !selectedProductsCookie ||
                    !subtotalAmountCookie ||
                    !taxAmountCookie ||
                    !totalAmountCookie ||
                    !selectedCompanyCookie) {
                    throw new Error('No invoice data found.');
                }

                const customerInfo = JSON.parse(customerInfoCookie);
                const selectedProducts = JSON.parse(selectedProductsCookie);
                const subtotalAmount = parseFloat(JSON.parse(subtotalAmountCookie));
                const taxAmount = parseFloat(JSON.parse(taxAmountCookie));
                const totalAmount = parseFloat(JSON.parse(totalAmountCookie));
                const selectedCompany = JSON.parse(selectedCompanyCookie);

                // Check if we have tax
                const isTaxAdded = (taxAmount > 0);

                // Build data for the backend, applying the same net=85% / tax=15% logic
                const invoiceData = {
                    date: new Date().toISOString().slice(0,10),
                    referenceNumber: customerInfo.referenceNumber || generateReferenceNumber(),
                    company: {
                        name: selectedCompany.name,
                        bankingInformation: selectedCompany.bankingInformation
                    },
                    billing: customerInfo.billing,
                    shipping: customerInfo.shipping || null,
                    paymentMethod: customerInfo.paymentMethod || 'N/A',
                    comments: customerInfo.comments || "",
                    salespersonName: customerInfo.salespersonName || "",
                    products: selectedProducts.map(product => {
                        if (isTaxAdded) {
                            const netPrice = Math.round(parseFloat(product.price * 0.85));
                            const taxPerUnit = Math.round(parseFloat(product.price * 0.15));
                            const lineTotal = Math.round(parseFloat(product.price * product.quantity));
                            return {
                                item_code: product.item_code,
                                name: product.name,
                                quantity: product.quantity,
                                unitPriceExcludingTax: netPrice,
                                taxPerUnit: taxPerUnit,
                                totalPrice: lineTotal
                            };
                        } else {
                            const lineTotal = Math.round(parseFloat(product.price * product.quantity));
                            return {
                                item_code: product.item_code,
                                name: product.name,
                                quantity: product.quantity,
                                unitPriceExcludingTax: Math.round(parseFloat(product.price)),
                                taxPerUnit: 0,
                                totalPrice: lineTotal
                            };
                        }
                    }),
                    subtotal: Math.round(parseFloat(subtotalAmount)),
                    tax: Math.round(parseFloat(taxAmount)),
                    total: Math.round(parseFloat(totalAmount))
                };

                console.log('Sending invoice data to backend:', invoiceData);

                // POST request to your PDF generation endpoint
                const response = await fetch('http://localhost:8000/api/generate-invoice', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(invoiceData)
                });

                if (!response.ok) {
                    let errorMessage = 'Failed to generate PDF.';
                    try {
                        const errorData = await response.json();
                        errorMessage = errorData.message || errorMessage;
                    } catch (e) {
                        console.error('Error parsing error response:', e);
                    }
                    throw new Error(errorMessage);
                }

                const successData = await response.json();
                console.log('Backend response:', successData);

                // Set a cookie to remember that this invoice has been confirmed
                setCookie('invoiceConfirmed', 'true', 7); // Keep for 7 days

                // Now that the request is successful, hide the confirm button
                confirmButton.style.display = 'none';

                // Show a success message
                showToastMessage('Invoice successfully sent!', 'success');

                // Add a more visible success message
                const successMessage = document.createElement('div');
                successMessage.style.textAlign = 'center';
                successMessage.style.margin = '20px 0';
                successMessage.style.padding = '15px';
                successMessage.style.backgroundColor = '#d4edda';
                successMessage.style.color = '#155724';
                successMessage.style.borderRadius = '5px';
                successMessage.style.fontWeight = 'bold';
                successMessage.innerHTML = 'Invoice has been successfully processed.<br>You can now download the image or return to the home page.';

                // Insert the message after the button container
                const buttonContainer = document.querySelector('.button-container');
                buttonContainer.parentNode.insertBefore(successMessage, buttonContainer.nextSibling);

            } catch (error) {
                console.error('Error generating PDF:', error);
                showToastMessage('An error occurred while sending the invoice. Please try again.', 'error');
            } finally {
                confirmButton.disabled = false;
                loadingSpinner.style.display = 'none';
            }
        }

        // Function to handle the edit order button click
        function handleEditOrder() {
            // Set a flag in sessionStorage to indicate we're editing an existing order
            sessionStorage.setItem('editingOrder', 'true');

            // Redirect back to the index page
            window.location.href = 'index.html';
        }

        document.addEventListener('DOMContentLoaded', function() {
            populateInvoice();

            // Add event listener for the confirm button
            document.getElementById('confirm-button').addEventListener('click', confirmInvoice);

            // Add event listener for the edit order button
            const editOrderButton = document.getElementById('edit-order-button');
            if (editOrderButton) {
                editOrderButton.addEventListener('click', handleEditOrder);
            }
        });

        // Screenshot functionality
        document.getElementById('screenshot-button').addEventListener('click', () => {
            const loadingSpinner = document.getElementById('loading-spinner');
            loadingSpinner.style.display = 'inline-block';
            const element = document.getElementById('pdf-1');

            // Get client name and reference number for the filename
            const customerInfoCookie = getCookie('customerInfo');
            let clientName = 'client';
            if (customerInfoCookie) {
                const customerInfo = JSON.parse(customerInfoCookie);
                if (customerInfo.billing && customerInfo.billing.name) {
                    // Replace spaces with underscores and remove special characters
                    clientName = customerInfo.billing.name.replace(/[^a-zA-Z0-9]/g, '_').replace(/_+/g, '_');
                }
            }

            // Get reference number
            const referenceElement = document.querySelector('.reference-number');
            let referenceNumber = 'ref';
            if (referenceElement) {
                const refText = referenceElement.textContent;
                const match = refText.match(/Reference Number:\s*([\w-]+)/);
                if (match && match[1]) {
                    referenceNumber = match[1];
                }
            }

            // Create dynamic filename
            const filename = `invoice-${clientName}-${referenceNumber}.png`;

            html2canvas(element, { scale: 2 })
                .then(canvas => {
                    const imgData = canvas.toDataURL('image/png');
                    const link = document.createElement('a');
                    link.href = imgData;
                    link.download = filename;
                    link.click();
                    loadingSpinner.style.display = 'none';
                    showToastMessage(`Image saved as ${filename}`, 'success');
                })
                .catch(err => {
                    console.error('Error capturing image:', err);
                    showToastMessage('Failed to capture image.', 'error');
                    loadingSpinner.style.display = 'none';
                });
        });
    </script>
</body>
</html>
