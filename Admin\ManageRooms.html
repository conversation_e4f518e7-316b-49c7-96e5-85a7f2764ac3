<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>Shans Inventory System - Manage Rooms</title>
  <style>
    /* Reset some default browser styles */
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      margin: 0;
      padding: 20px;
      background-color: #f4f4f4;
    }

    .container {
      max-width: 1200px;
      margin: auto;
    }

    h1 {
      color: #333;
      text-align: center;
      margin-bottom: 20px;
    }

    .room-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }

    .room-block {
      background: #fff;
      border: 1px solid #ddd;
      border-radius: 5px;
      padding: 20px;
      text-align: center;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
      transition: transform 0.3s ease;
      position: relative;
    }

    .room-block:hover {
      transform: translateY(-5px);
    }

    .add-room {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 48px;
      color: #888;
      cursor: pointer;
      background-color: #e0e0e0;
    }

    .add-room:hover {
      background-color: #d5d5d5;
    }

    .modal {
      display: none; /* Hidden by default */
      position: fixed; /* Stay in place */
      z-index: 1000; /* Sit on top */
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      overflow: auto; /* Enable scroll if needed */
      background-color: rgba(0,0,0,0.5); /* Black w/ opacity */
    }

    .modal-content {
      background-color: #fefefe;
      margin: 10% auto; /* 10% from the top and centered */
      padding: 20px;
      border: 1px solid #888;
      width: 90%;
      max-width: 500px;
      border-radius: 5px;
      position: relative;
    }

    .close {
      color: #aaa;
      position: absolute;
      top: 10px;
      right: 20px;
      font-size: 28px;
      font-weight: bold;
      cursor: pointer;
    }

    .close:hover,
    .close:focus {
      color: #000;
      text-decoration: none;
    }

    form {
      margin-top: 20px;
    }

    .form-group {
      margin-bottom: 15px;
    }

    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }

    input[type="text"],
    input[type="color"] {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      box-sizing: border-box;
    }

    input[type="color"] {
      height: 40px;
    }

    button {
      display: inline-block;
      padding: 10px 20px;
      background: #333;
      color: #fff;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
    }

    button:hover {
      background: #555;
    }

    /* Edit and Delete Button Styles */
    .room-block button {
      margin: 5px 5px 0 5px;
      padding: 5px 10px;
      border: none;
      border-radius: 3px;
      cursor: pointer;
      font-size: 14px;
    }

    .edit-button {
      background-color: #4CAF50;
      color: white;
    }

    .edit-button:hover {
      background-color: #45a049;
    }

    .delete-button {
      background-color: #f44336;
      color: white;
    }

    .delete-button:hover {
      background-color: #da190b;
    }

    /* Responsive Design */
    @media (max-width: 600px) {
      .room-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      }
    }

    /* Toast Container and Styles */
    .toast {
      position: fixed;
      top: 20px;
      right: 20px;
      background-color: #333;
      color: #fff;
      padding: 10px 20px;
      border-radius: 4px;
      opacity: 1;
      z-index: 9999;
      font-size: 14px;
      animation: fadeInOut 4s forwards; /* Show, then fade out automatically */
    }

    .toast-success {
      background-color: #4CAF50;
    }

    .toast-error {
      background-color: #f44336;
    }

    .toast-info {
      background-color: #2196F3;
    }

    /* Fade in/out animation */
    @keyframes fadeInOut {
      0% {
        opacity: 0;
      }
      10% {
        opacity: 1;
      }
      90% {
        opacity: 1;
      }
      100% {
        opacity: 0;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
      <div>
        <h1 style="margin: 0;">Inventory Rooms</h1>
        <a href="index.html" style="text-decoration: none; color: #007BFF; font-size: 16px;">&larr; Back to Home</a>
      </div>
      <button onclick="logout()" style="background-color: #e74c3c; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px;">Logout</button>
    </div>
    <div class="room-grid" id="roomGrid">
      <div class="room-block add-room" id="addRoomBlock" aria-label="Add new room" title="Add New Room">
        +
      </div>
    </div>
  </div>

  <!-- Add Room Modal -->
  <div id="addRoomModal" class="modal" aria-hidden="true" role="dialog" aria-labelledby="addRoomModalTitle">
    <div class="modal-content">
      <span class="close" id="closeAddModal" aria-label="Close modal">&times;</span>
      <h2 id="addRoomModalTitle">Add New Room</h2>
      <form id="addRoomForm">
        <div class="form-group">
          <label for="roomName">Room Name:</label>
          <input type="text" id="roomName" name="roomName" required>
        </div>
        <div class="form-group">
          <label for="roomColor">Room Color:</label>
          <input type="color" id="roomColor" name="roomColor" value="#ffffff" required>
        </div>
        <div class="form-group">
          <label for="roomLocation">Room Location:</label>
          <input type="text" id="roomLocation" name="roomLocation" required>
        </div>
        <button type="submit">Add Room</button>
      </form>
    </div>
  </div>

  <!-- Edit Room Modal -->
  <div id="editRoomModal" class="modal" aria-hidden="true" role="dialog" aria-labelledby="editRoomModalTitle">
    <div class="modal-content">
      <span class="close" id="closeEditModal" aria-label="Close modal">&times;</span>
      <h2 id="editRoomModalTitle">Edit Room</h2>
      <form id="editRoomForm">
        <input type="hidden" id="editRoomId" name="editRoomId">
        <div class="form-group">
          <label for="editRoomName">Room Name:</label>
          <input type="text" id="editRoomName" name="editRoomName" required>
        </div>
        <div class="form-group">
          <label for="editRoomColor">Room Color:</label>
          <input type="color" id="editRoomColor" name="editRoomColor" value="#ffffff" required>
        </div>
        <div class="form-group">
          <label for="editRoomLocation">Room Location:</label>
          <input type="text" id="editRoomLocation" name="editRoomLocation" required>
        </div>
        <button type="submit">Update Room</button>
      </form>
    </div>
  </div>

  <script>
    // Define the API URLs
    const ROOMS_API_URL = 'https://shans-backend-1.onrender.com/api/rooms';
    const PRODUCTS_API_URL = 'https://shans-backend-1.onrender.com/api/products';
    const API_BASE_URL = 'https://shans-backend-1.onrender.com/api';

    // Check authentication status
    async function checkAuthStatus() {
      const token = localStorage.getItem('authToken');
      const userInfo = localStorage.getItem('userInfo');

      if (!token || !userInfo) {
        window.location.href = '../login.html';
        return false;
      }

      const user = JSON.parse(userInfo);
      if (!user.is_admin) {
        localStorage.removeItem('authToken');
        localStorage.removeItem('userInfo');
        window.location.href = '../login.html';
        return false;
      }

      try {
        // Verify token with backend
        const response = await fetch(`${API_BASE_URL}/auth/me`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (!response.ok) {
          localStorage.removeItem('authToken');
          localStorage.removeItem('userInfo');
          window.location.href = '../login.html';
          return false;
        }

        const data = await response.json();
        if (!data.user.is_admin) {
          localStorage.removeItem('authToken');
          localStorage.removeItem('userInfo');
          window.location.href = '../login.html';
          return false;
        }

        return true;
      } catch (error) {
        console.error('Auth check failed:', error);
        localStorage.removeItem('authToken');
        localStorage.removeItem('userInfo');
        window.location.href = '../login.html';
        return false;
      }
    }

    // Logout functionality
    function logout() {
      fetch(`${API_BASE_URL}/auth/logout`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        }
      })
      .then(() => {
        localStorage.removeItem('authToken');
        localStorage.removeItem('userInfo');
        window.location.href = 'login.html';
      })
      .catch(error => {
        console.error('Logout error:', error);
        localStorage.removeItem('authToken');
        localStorage.removeItem('userInfo');
        window.location.href = 'login.html';
      });
    }

    // Get DOM elements
    const roomGrid = document.getElementById('roomGrid');
    const addRoomBlock = document.getElementById('addRoomBlock');
    const addRoomModal = document.getElementById('addRoomModal');
    const closeAddModalBtn = document.getElementById('closeAddModal');
    const addRoomForm = document.getElementById('addRoomForm');

    const editRoomModal = document.getElementById('editRoomModal');
    const closeEditModalBtn = document.getElementById('closeEditModal');
    const editRoomForm = document.getElementById('editRoomForm');

    /**
     * Show a toast message
     * @param {string} message - The toast message
     * @param {string} type - The type of message ('success', 'error', 'info')
     */
    function showToast(message, type = 'info') {
      const toast = document.createElement('div');
      toast.classList.add('toast', `toast-${type}`);
      toast.textContent = message;
      document.body.appendChild(toast);

      // The toast uses a CSS animation to fade out after some time
      // If you want to remove it after the animation, you can do so with a timer:
      setTimeout(() => {
        toast.remove();
      }, 4000); // 4s matches the animation duration
    }

    /**
     * Function to create a room block element
     * @param {Object} room - The room data
     * @returns {HTMLElement} - The room block element
     */
    function createRoomBlock(room) {
      const roomBlock = document.createElement('div');
      roomBlock.className = 'room-block';
      roomBlock.style.backgroundColor = room.color;
      roomBlock.innerHTML = `
        <h3>${room.name}</h3>
        <p>${room.location}</p>
        <button class="edit-button" data-id="${room.id}">Edit</button>
        <button class="delete-button" data-id="${room.id}">Delete</button>
      `;
      return roomBlock;
    }

    /**
     * Fetch all rooms from the backend and display them
     */
    async function fetchRooms() {
      try {
        const response = await fetch(ROOMS_API_URL);
        if (!response.ok) {
          throw new Error(`Network response was not ok (${response.status})`);
        }
        const rooms = await response.json();

        // Clear existing room blocks except the add-room block
        roomGrid.querySelectorAll('.room-block:not(.add-room)').forEach(block => block.remove());

        // Add each room to the grid
        rooms.forEach(room => {
          const roomElement = createRoomBlock(room);
          roomGrid.insertBefore(roomElement, addRoomBlock);
        });
      } catch (error) {
        console.error('Error fetching rooms:', error);
        showToast('Failed to load rooms. Please try again later.', 'error');
      }
    }

    /**
     * Open the Add Room modal
     */
    function openAddModal() {
      addRoomModal.style.display = 'block';
      addRoomModal.setAttribute('aria-hidden', 'false');
    }

    /**
     * Close the Add Room modal
     */
    function closeAddModal() {
      addRoomModal.style.display = 'none';
      addRoomModal.setAttribute('aria-hidden', 'true');
      addRoomForm.reset();
    }

    /**
     * Open the Edit Room modal with pre-filled data
     * @param {number} id - The ID of the room to edit
     */
    async function openEditModal(id) {
      try {
        const response = await fetch(`${ROOMS_API_URL}/${id}`);
        if (!response.ok) {
          throw new Error(`Room not found (Status: ${response.status})`);
        }
        const room = await response.json();

        // Fill the form with existing room data
        document.getElementById('editRoomId').value = room.id;
        document.getElementById('editRoomName').value = room.name;
        document.getElementById('editRoomColor').value = room.color;
        document.getElementById('editRoomLocation').value = room.location;

        // Show the edit modal
        editRoomModal.style.display = 'block';
        editRoomModal.setAttribute('aria-hidden', 'false');
      } catch (error) {
        console.error('Error fetching room:', error);
        // You could show a toast error here if desired:
        // showToast('Failed to load room details.', 'error');
      }
    }

    /**
     * Close the Edit Room modal
     */
    function closeEditModal() {
      editRoomModal.style.display = 'none';
      editRoomModal.setAttribute('aria-hidden', 'true');
      editRoomForm.reset();
    }

    /**
     * Handle the submission of the Add Room form
     */
    addRoomForm.addEventListener('submit', async (e) => {
      e.preventDefault();

      const roomName = document.getElementById('roomName').value.trim();
      const roomColor = document.getElementById('roomColor').value;
      const roomLocation = document.getElementById('roomLocation').value.trim();

      if (!roomName || !roomLocation) {
        showToast('Please fill in all required fields.', 'error');
        return;
      }

      const newRoom = {
        name: roomName,
        color: roomColor,
        location: roomLocation
      };

      try {
        const response = await fetch(ROOMS_API_URL, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(newRoom)
        });

        if (response.ok) {
          const createdRoom = await response.json();
          const roomElement = createRoomBlock(createdRoom);
          roomGrid.insertBefore(roomElement, addRoomBlock);
          closeAddModal();
          showToast('Room added successfully.', 'success');
        } else {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Failed to add room.');
        }
      } catch (error) {
        console.error('Error adding room:', error);
        showToast(`An error occurred while adding the room: ${error.message}`, 'error');
      }
    });

    /**
     * Handle the submission of the Edit Room form
     */
    editRoomForm.addEventListener('submit', async (e) => {
      e.preventDefault();

      const id = document.getElementById('editRoomId').value;
      const roomName = document.getElementById('editRoomName').value.trim();
      const roomColor = document.getElementById('editRoomColor').value;
      const roomLocation = document.getElementById('editRoomLocation').value.trim();

      if (!roomName || !roomLocation) {
        showToast('Please fill in all required fields.', 'error');
        return;
      }

      const updatedRoom = {
        name: roomName,
        color: roomColor,
        location: roomLocation
      };

      try {
        const response = await fetch(`${ROOMS_API_URL}/${id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(updatedRoom)
        });

        if (response.ok) {
          const room = await response.json();
          // Update the room block in the UI
          const roomBlock = document.querySelector(`.edit-button[data-id='${id}']`).parentElement;
          roomBlock.style.backgroundColor = room.color;
          roomBlock.querySelector('h3').textContent = room.name;
          roomBlock.querySelector('p').textContent = room.location;
          closeEditModal();
          showToast('Room updated successfully.', 'success');
        } else {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Failed to update room.');
        }
      } catch (error) {
        console.error('Error updating room:', error);
        showToast(`An error occurred while updating the room: ${error.message}`, 'error');
      }
    });

    /**
     * Handle Delete Room action
     * @param {number} id - The ID of the room to delete
     */
     async function deleteRoom(id) {
    if (!confirm('Are you sure you want to delete this room? This will also delete all products linked to this room.')) return;

    try {
        // Delete the room (the backend will handle cascading deletion of associated products)
        const response = await fetch(`${ROOMS_API_URL}/${id}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            if (response.headers.get('content-type')?.includes('application/json')) {
                const errorData = await response.json();
                throw new Error(errorData.message || 'Failed to delete the room');
            } else {
                throw new Error(`Server error: ${response.status}`);
            }
        }

        // Remove the room block from the UI
        const roomBlock = document.querySelector(`.delete-button[data-id='${id}']`).parentElement;
        if (roomBlock) {
            roomBlock.remove();
            showToast('Room and its associated products deleted successfully.', 'success');
        }
    } catch (error) {
        console.error('Error deleting room:', error);
        showToast('Failed to delete room. Please try again later.', 'error');
    }
}
    /**
     * Handle Delete Room form submission
     * @param {number} id - The ID of the room to delete
     */
    async function handleDeleteRoom(id) {
      await deleteRoom(id);
    }

    /**
     * Event delegation for Edit and Delete buttons
     */
    roomGrid.addEventListener('click', (e) => {
      if (e.target.classList.contains('edit-button')) {
        const id = e.target.getAttribute('data-id');
        openEditModal(id);
      }

      if (e.target.classList.contains('delete-button')) {
        const id = e.target.getAttribute('data-id');
        handleDeleteRoom(id);
      }
    });

    /**
     * Event listeners for opening and closing modals
     */
    addRoomBlock.addEventListener('click', openAddModal);
    closeAddModalBtn.addEventListener('click', closeAddModal);
    closeEditModalBtn.addEventListener('click', closeEditModal);

    window.addEventListener('click', (event) => {
      if (event.target === addRoomModal) {
        closeAddModal();
      }
      if (event.target === editRoomModal) {
        closeEditModal();
      }
    });

    // Initialize page
    window.onload = async function() {
      const isAuthenticated = await checkAuthStatus();
      if (!isAuthenticated) {
        return;
      }
      // Fetch and display rooms on page load
      fetchRooms();
    };
  </script>
</body>
</html>
