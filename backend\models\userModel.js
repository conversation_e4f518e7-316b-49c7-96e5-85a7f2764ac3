const pool = require('../database');
const bcrypt = require('bcryptjs');

class UserModel {
  // Create users table if it doesn't exist
  static async initializeUsersTable() {
    const client = await pool.connect();
    try {
      await client.query(`
        CREATE TABLE IF NOT EXISTS users (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          email TEXT UNIQUE NOT NULL,
          password_hash TEXT NOT NULL,
          is_admin INTEGER DEFAULT 0,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Create default admin user if no users exist
      const userCount = await client.query('SELECT COUNT(*) as count FROM users');
      if (parseInt(userCount.rows[0].count) === 0) {
        const hashedPassword = await bcrypt.hash('admin123', 10);
        await client.query(
          'INSERT INTO users (email, password_hash, is_admin) VALUES (?, ?, ?)',
          ['<EMAIL>', hashedPassword, 1]
        );
        console.log('Default admin user created: <EMAIL>');
      }
    } finally {
      client.release();
    }
  }

  // Create a new user
  static async createUser(email, password, isAdmin = false) {
    const client = await pool.connect();
    try {
      const hashedPassword = await bcrypt.hash(password, 10);
      const result = await client.query(
        'INSERT INTO users (email, password_hash, is_admin) VALUES (?, ?, ?) RETURNING id, email, is_admin, created_at',
        [email, hashedPassword, isAdmin ? 1 : 0]
      );
      return result.rows[0];
    } finally {
      client.release();
    }
  }

  // Get user by email
  static async getUserByEmail(email) {
    const client = await pool.connect();
    try {
      const result = await client.query(
        'SELECT id, email, password_hash, is_admin, created_at FROM users WHERE email = ?',
        [email]
      );
      const user = result.rows[0];
      if (user) {
        user.is_admin = Boolean(user.is_admin);
      }
      return user;
    } finally {
      client.release();
    }
  }

  // Get user by ID
  static async getUserById(id) {
    const client = await pool.connect();
    try {
      const result = await client.query(
        'SELECT id, email, is_admin, created_at FROM users WHERE id = ?',
        [id]
      );
      const user = result.rows[0];
      if (user) {
        user.is_admin = Boolean(user.is_admin);
      }
      return user;
    } finally {
      client.release();
    }
  }

  // Get all users
  static async getAllUsers() {
    const client = await pool.connect();
    try {
      const result = await client.query(
        'SELECT id, email, is_admin, created_at FROM users ORDER BY created_at DESC'
      );
      return result.rows.map(user => ({
        ...user,
        is_admin: Boolean(user.is_admin)
      }));
    } finally {
      client.release();
    }
  }

  // Update user
  static async updateUser(id, email, isAdmin) {
    const client = await pool.connect();
    try {
      const result = await client.query(
        'UPDATE users SET email = ?, is_admin = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? RETURNING id, email, is_admin, updated_at',
        [email, isAdmin ? 1 : 0, id]
      );
      const user = result.rows[0];
      if (user) {
        user.is_admin = Boolean(user.is_admin);
      }
      return user;
    } finally {
      client.release();
    }
  }

  // Update user password
  static async updateUserPassword(id, newPassword) {
    const client = await pool.connect();
    try {
      const hashedPassword = await bcrypt.hash(newPassword, 10);
      await client.query(
        'UPDATE users SET password_hash = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [hashedPassword, id]
      );
      return true;
    } finally {
      client.release();
    }
  }

  // Delete user
  static async deleteUser(id) {
    const client = await pool.connect();
    try {
      const result = await client.query('DELETE FROM users WHERE id = ?', [id]);
      return result.rowCount > 0;
    } finally {
      client.release();
    }
  }

  // Verify password
  static async verifyPassword(plainPassword, hashedPassword) {
    return await bcrypt.compare(plainPassword, hashedPassword);
  }

  // Check if user is admin
  static async isAdmin(userId) {
    const client = await pool.connect();
    try {
      const result = await client.query(
        'SELECT is_admin FROM users WHERE id = ?',
        [userId]
      );
      return Boolean(result.rows[0]?.is_admin) || false;
    } finally {
      client.release();
    }
  }
}

module.exports = UserModel;
