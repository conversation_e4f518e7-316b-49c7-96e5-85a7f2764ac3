// run-migrations.js
require('dotenv').config();
const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Get all migration files
const migrationsDir = path.join(__dirname, 'migrations');
const migrationFiles = fs.readdirSync(migrationsDir)
  .filter(file => file.endsWith('.js'))
  .sort(); // Sort to ensure migrations run in order

console.log('Running migrations...');

// Run each migration
migrationFiles.forEach(file => {
  const migrationPath = path.join(migrationsDir, file);
  console.log(`Running migration: ${file}`);

  try {
    // Use double quotes for Windows paths that might contain spaces
    execSync(`node "${migrationPath}"`, { stdio: 'inherit' });
    console.log(`Migration ${file} completed successfully.`);
  } catch (error) {
    console.error(`Error running migration ${file}:`, error.message);
    process.exit(1);
  }
});

console.log('All migrations completed successfully.');
