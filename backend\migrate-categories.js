// Direct script to run the categories migration
require('dotenv').config();
const path = require('path');
const { execSync } = require('child_process');

console.log('Running categories migration directly...');

try {
  const migrationPath = path.join(__dirname, 'migrations', 'add_categories_table.js');
  console.log(`Migration path: ${migrationPath}`);
  
  // Use double quotes for Windows paths that might contain spaces
  execSync(`node "${migrationPath}"`, { stdio: 'inherit' });
  console.log('Categories migration completed successfully.');
} catch (error) {
  console.error('Error running categories migration:', error.message);
  process.exit(1);
}
