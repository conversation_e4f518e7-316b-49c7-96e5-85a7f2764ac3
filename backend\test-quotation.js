const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

async function testQuotation() {
  const quotationData = {
    date: new Date().toISOString().slice(0, 10),
    referenceNumber: 'TEST-' + Date.now(),
    billing: {
      name: 'Test Customer',
      address: '123 Test St',
      email: '<EMAIL>',
      phone: '**********'
    },
    shipping: null,
    products: [
      {
        item_code: 'TEST-001',
        name: 'Test Product',
        quantity: 1,
        unitPriceExcludingTax: 100.00,
        taxPerUnit: 15.00,
        totalPrice: 115.00
      }
    ],
    subtotal: 100.00,
    tax: 15.00,
    total: 115.00,
    paymentMethod: 'Cash',
    company: {
      name: 'Test Company',
      bankingInformation: 'Test Bank Info'
    },
    comments: 'Test comment',
    salespersonName: 'Test Salesperson'
  };

  console.log('Sending test quotation data:', JSON.stringify(quotationData, null, 2));

  try {
    const response = await fetch('https://shans-backend-1.onrender.com/api/generate-quotation', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(quotationData)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error response:', errorText);
      throw new Error(`Failed to generate quotation: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    console.log('Success! Quotation generated:', result);
  } catch (error) {
    console.error('Error generating quotation:', error);
  }
}

testQuotation();
