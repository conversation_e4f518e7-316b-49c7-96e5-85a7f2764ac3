<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shan<PERSON> Ad<PERSON> - Login</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .login-container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }

        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: #e74c3c;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }

        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 28px;
        }

        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 14px;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }

        label {
            display: block;
            margin-bottom: 5px;
            color: #555;
            font-weight: 500;
        }

        input[type="email"],
        input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e1e1;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }

        input[type="email"]:focus,
        input[type="password"]:focus {
            outline: none;
            border-color: #e74c3c;
        }

        .login-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .login-btn:hover {
            transform: translateY(-2px);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .error-message {
            color: #e74c3c;
            margin-top: 15px;
            padding: 10px;
            background: #fdf2f2;
            border-radius: 5px;
            display: none;
        }

        .success-message {
            color: #27ae60;
            margin-top: 15px;
            padding: 10px;
            background: #f2fdf2;
            border-radius: 5px;
            display: none;
        }

        .loading {
            display: none;
            margin-top: 15px;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #e74c3c;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .footer {
            margin-top: 30px;
            color: #888;
            font-size: 14px;
        }

        .admin-badge {
            background: #e74c3c;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 20px;
        }

        @media (max-width: 480px) {
            .login-container {
                margin: 20px;
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">A</div>
        <h1>Admin Portal</h1>
        <div class="subtitle">
            <span class="admin-badge">ADMIN ACCESS</span><br>
            Please sign in with your administrator credentials
        </div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="email">Email Address</label>
                <input type="email" id="email" name="email" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <button type="submit" class="login-btn" id="loginBtn">
                Sign In to Admin
            </button>
        </form>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>Verifying admin credentials...</p>
        </div>

        <div class="error-message" id="errorMessage"></div>
        <div class="success-message" id="successMessage"></div>

        <div class="footer">
            <p>Shans Admin System &copy; 2024</p>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8000/api';
        
        const loginForm = document.getElementById('loginForm');
        const loginBtn = document.getElementById('loginBtn');
        const loading = document.getElementById('loading');
        const errorMessage = document.getElementById('errorMessage');
        const successMessage = document.getElementById('successMessage');

        function showError(message) {
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
            successMessage.style.display = 'none';
        }

        function showSuccess(message) {
            successMessage.textContent = message;
            successMessage.style.display = 'block';
            errorMessage.style.display = 'none';
        }

        function hideMessages() {
            errorMessage.style.display = 'none';
            successMessage.style.display = 'none';
        }

        function setLoading(isLoading) {
            if (isLoading) {
                loginForm.style.display = 'none';
                loading.style.display = 'block';
                loginBtn.disabled = true;
            } else {
                loginForm.style.display = 'block';
                loading.style.display = 'none';
                loginBtn.disabled = false;
            }
        }

        // Check if admin is already logged in
        function checkAuthStatus() {
            const token = localStorage.getItem('authToken');
            const userInfo = localStorage.getItem('userInfo');
            
            if (token && userInfo) {
                const user = JSON.parse(userInfo);
                if (user.is_admin) {
                    // Verify token with backend
                    fetch(`${API_BASE_URL}/auth/me`, {
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    })
                    .then(response => {
                        if (response.ok) {
                            return response.json();
                        } else {
                            throw new Error('Token invalid');
                        }
                    })
                    .then(data => {
                        if (data.user.is_admin) {
                            // Admin is already authenticated, redirect to admin dashboard
                            window.location.href = 'index.html';
                        } else {
                            // User is not admin, clear storage
                            localStorage.removeItem('authToken');
                            localStorage.removeItem('userInfo');
                        }
                    })
                    .catch(error => {
                        console.error('Auth check failed:', error);
                        localStorage.removeItem('authToken');
                        localStorage.removeItem('userInfo');
                    });
                } else {
                    // User is not admin, clear storage
                    localStorage.removeItem('authToken');
                    localStorage.removeItem('userInfo');
                }
            }
        }

        loginForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            hideMessages();
            setLoading(true);

            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;

            try {
                const response = await fetch(`${API_BASE_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password }),
                });

                const data = await response.json();

                if (response.ok) {
                    // Check if user is admin
                    if (!data.user.is_admin) {
                        showError('Access denied. Admin privileges required.');
                        setLoading(false);
                        return;
                    }

                    // Store authentication token and user info
                    localStorage.setItem('authToken', data.token);
                    localStorage.setItem('userInfo', JSON.stringify(data.user));
                    
                    showSuccess('Admin login successful! Redirecting...');
                    
                    // Redirect after a short delay
                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 1500);
                } else {
                    showError(data.message || 'Login failed');
                    setLoading(false);
                }
            } catch (error) {
                console.error('Login error:', error);
                showError('Network error. Please try again.');
                setLoading(false);
            }
        });

        // Check auth status on page load
        window.addEventListener('load', checkAuthStatus);
    </script>
</body>
</html>
