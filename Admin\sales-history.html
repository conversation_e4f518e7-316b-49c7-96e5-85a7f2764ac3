<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Shans Inventory System - Sales History</title>
    <style>
        :root {
            --primary-color: #4a90e2;
            --secondary-color: #f5f5f5;
            --text-color: #333;
            --border-color: #ddd;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--secondary-color);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        h1 {
            text-align: center;
            margin-bottom: 20px;
            color: var(--primary-color);
        }

        .controls {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 10px;
        }

        .controls-left, .controls-right {
            display: flex;
            gap: 10px;
        }

        .view-toggle, .search-bar, .action-button {
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 5px;
        }

        .view-toggle, .action-button {
            background-color: var(--primary-color);
            color: white;
            cursor: pointer;
            transition: background-color 0.3s ease;
            white-space: nowrap;
        }

        .view-toggle:hover, .action-button:hover {
            background-color: #357ab8;
        }

        .print-button {
            background-color: #28a745;
        }

        .print-button:hover {
            background-color: #218838;
        }

        .excel-button {
            background-color: #217346;
        }

        .excel-button:hover {
            background-color: #1e6e41;
        }

        .search-bar {
            width: 300px;
            transition: all 0.3s ease;
            flex-grow: 1;
        }

        .search-bar:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
        }

        .month-section {
            margin-bottom: 40px;
        }

        .month-header {
            background-color: #f8f9fa;
            color: #333;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 15px;
            font-size: 1.3em;
            font-weight: 600;
            border-left: 4px solid var(--primary-color);
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        /* Excel-like table styling */
        .table-container {
            overflow-x: auto;
            max-width: 100%;
            margin-bottom: 20px;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background-color: white;
            font-size: 14px;
            border: 1px solid #e0e0e0;
            margin-bottom: 0;
        }

        th, td {
            text-align: left;
            padding: 8px 10px;
            border: 1px solid #e0e0e0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 200px;
        }

        th {
            position: sticky;
            top: 0;
            background-color: #f2f2f2;
            color: #333;
            font-weight: bold;
            border-bottom: 2px solid #d0d0d0;
            z-index: 10;
        }

        tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        tr:hover {
            background-color: #f0f7ff;
        }

        /* Column specific styling */
        td:nth-child(1), th:nth-child(1) { /* Reference Number */
            min-width: 120px;
            font-weight: 500;
        }

        td:nth-child(2), th:nth-child(2) { /* Date */
            min-width: 100px;
        }

        td:nth-child(3), th:nth-child(3) { /* Client Name */
            min-width: 150px;
            font-weight: 500;
        }

        td:nth-child(4), th:nth-child(4) { /* Billing Address */
            min-width: 200px;
        }

        td:nth-child(5), th:nth-child(5) { /* Billing Email */
            min-width: 180px;
        }

        td:nth-child(7), th:nth-child(7) { /* Salesperson */
            min-width: 120px;
        }

        td:nth-child(8), th:nth-child(8) { /* Company */
            min-width: 150px;
        }

        td:nth-child(9), th:nth-child(9) { /* Item Purchase */
            min-width: 180px;
        }

        td:nth-child(11), th:nth-child(11), /* Quantity */
        td:nth-child(12), th:nth-child(12), /* Unit Cost */
        td:nth-child(13), th:nth-child(13), /* Unit Price */
        td:nth-child(14), th:nth-child(14), /* Profit per Unit */
        td:nth-child(15), th:nth-child(15), /* Tax per Unit */
        td:nth-child(17), th:nth-child(17), /* Total */
        td:nth-child(18), th:nth-child(18) { /* Total Profit */
            text-align: right;
            min-width: 100px;
        }

        /* Profit columns styling */
        td:nth-child(14), td:nth-child(18) {
            background-color: #f0fff0;
        }

        /* Month Summary Section */
        .month-summary {
            background-color: white;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            margin-top: 15px;
            margin-bottom: 30px;
            padding: 15px 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }

        .summary-label {
            font-weight: 600;
            color: #555;
        }

        .summary-value {
            font-weight: bold;
            color: #333;
        }

        .grand-total {
            background-color: #e6f2ff;
            border-left: 4px solid #0066cc;
        }

        .grand-total .summary-label {
            font-size: 16px;
            color: #333;
        }

        .grand-total .summary-value {
            font-size: 18px;
            color: #0066cc;
        }

        .profit-total {
            background-color: #e6ffe6;
            border-left: 4px solid #28a745;
        }

        .profit-total .summary-label {
            font-size: 15px;
            color: #333;
        }

        .profit-total .summary-value {
            font-size: 16px;
            color: #28a745;
        }

        .card-view {
            display: none;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }

        .card {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: 1px solid #e0e0e0;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .card h3 {
            color: var(--primary-color);
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
            font-size: 18px;
        }

        .card p {
            margin-bottom: 8px;
            font-size: 14px;
            display: flex;
            justify-content: space-between;
        }

        .card p strong {
            font-weight: 600;
            color: #555;
            margin-right: 10px;
        }

        @media (max-width: 768px) {
            .controls-left, .controls-right {
                width: 100%;
            }

            .controls-left {
                display: grid;
                grid-template-columns: 1fr 1fr;
            }

            .search-bar {
                width: 100%;
            }

            .table-container {
                overflow-x: auto;
                max-width: 100%;
            }

            /* Auto-switch to card view on mobile */
            #tables-container {
                display: none !important;
            }

            .card-view {
                display: grid !important;
                grid-template-columns: 1fr;
            }

            .card {
                margin-bottom: 15px;
            }

            .month-summary {
                margin-top: 10px;
                margin-bottom: 20px;
            }

            .summary-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }

            .summary-value {
                align-self: flex-end;
                width: 100%;
                text-align: right;
            }
        }

        /* Print button styling */
        @media print {
            body * {
                visibility: visible;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="index.html" style="text-decoration: none; color: #007BFF; font-size: 16px; margin-right: 20px;">&larr; Back to Home</a>
        <h1>Sales Dashboard</h1>
        <div class="controls">
            <div class="controls-left">
                <button class="view-toggle">Toggle View</button>
                <button class="action-button print-button" id="print-button">Print</button>
                <button class="action-button excel-button" id="save-to-excel">Export to Excel</button>
            </div>
            <div class="controls-right">
                <input type="text" class="search-bar" placeholder="Search sales by any field...">
            </div>
        </div>
        <div id="tables-container">
            <!-- Monthly Tables Will Be Dynamically Added Here -->
        </div>
        <div class="card-view" id="card-view">
            <!-- Cards Will Be Dynamically Added Here -->
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/xlsx/dist/xlsx.full.min.js"></script>
    <style media="print">
        /* Print-specific styles */
        body {
            font-size: 12px;
            background-color: white;
        }

        .container {
            max-width: 100%;
            padding: 0;
            margin: 0;
        }

        .controls, .view-toggle, #print-button, #save-to-excel, a[href="index.html"] {
            display: none !important;
        }

        .month-header {
            background-color: #f2f2f2 !important;
            color: black !important;
            padding: 10px !important;
            margin-bottom: 10px !important;
            page-break-before: always;
            border-left: 3px solid #333 !important;
            font-size: 16px !important;
            font-weight: bold !important;
        }

        table {
            width: 100% !important;
            border-collapse: collapse !important;
            page-break-inside: avoid;
            font-size: 10px !important;
        }

        th {
            background-color: #f2f2f2 !important;
            color: black !important;
            font-weight: bold !important;
        }

        th, td {
            border: 1px solid #ddd !important;
            padding: 4px !important;
        }

        .month-summary {
            margin-top: 10px !important;
            margin-bottom: 20px !important;
            padding: 10px !important;
            border: 1px solid #ddd !important;
            page-break-inside: avoid;
        }

        .summary-item {
            padding: 5px 10px !important;
            margin-bottom: 5px !important;
            border: 1px solid #eee !important;
        }

        .grand-total {
            background-color: #f2f2f2 !important;
            border-left: 3px solid #333 !important;
        }

        .profit-total {
            background-color: #f2f2f2 !important;
            border-left: 3px solid #28a745 !important;
        }

        .card-view {
            display: none !important;
        }
    </style>

    <script>
        document.getElementById('save-to-excel').addEventListener('click', saveToExcel);
        document.getElementById('print-button').addEventListener('click', printSalesData);

        const tablesContainer = document.getElementById('tables-container');
        const cardView = document.getElementById('card-view');
        const viewToggle = document.querySelector('.view-toggle');
        const searchBar = document.querySelector('.search-bar');

        let allSalesData = [];
        let groupedSalesData = {};

        async function fetchSalesData() {
            try {
                const response = await fetch('https://shans-backend-1.onrender.com/api/sales');
                if (!response.ok) {
                    throw new Error('Network response was not ok.');
                }
                const data = await response.json();
                console.log('Raw data from API:', data);

                // Fetch all products to get room information
                const productsResponse = await fetch('https://shans-backend-1.onrender.com/api/products');
                if (!productsResponse.ok) {
                    throw new Error('Failed to fetch products data.');
                }
                const productsData = await productsResponse.json();

                // Create maps for product information lookup
                const productRoomMap = {};
                const productCostMap = {};
                productsData.forEach(product => {
                    productRoomMap[product.item_code] = product.room_name;
                    productCostMap[product.item_code] = {
                        unit_cost: parseFloat(product.unit_cost) || 0,
                        unit_retail_price: parseFloat(product.unit_retail_price) || 0
                    };
                });

                // Add room information to each sale item
                data.forEach(sale => {
                    sale.items.forEach(item => {
                        // Add room information
                        item.room_name = productRoomMap[item.item_code] || 'N/A';

                        // Get cost information from product data if available
                        if (productCostMap[item.item_code]) {
                            // Store the unit cost from the product data for profit calculations
                            item.unit_cost = productCostMap[item.item_code].unit_cost;
                        } else {
                            // If product not found, ensure unit_cost is set to 0 to avoid NaN in calculations
                            item.unit_cost = 0;
                        }
                    });
                });

                allSalesData = data;
                groupDataByMonth(allSalesData);
                renderTables(groupedSalesData);
                renderCards(allSalesData);
            } catch (error) {
                console.error('Error fetching sales data:', error);
                tablesContainer.innerHTML = '<p style="color: red;">Failed to load sales data. Please try again later.</p>';
            }
        }

        function groupDataByMonth(data) {
            groupedSalesData = {};
            data.forEach(item => {
                const date = new Date(item.date);
                const monthYear = date.toLocaleString('default', { month: 'long', year: 'numeric' });
                if (!groupedSalesData[monthYear]) {
                    groupedSalesData[monthYear] = [];
                }
                groupedSalesData[monthYear].push(item);
            });
        }

        function renderTables(groupedData) {
            tablesContainer.innerHTML = '';

            if (Object.keys(groupedData).length === 0) {
                tablesContainer.innerHTML = '<p style="text-align: center;">No sales data found for your search.</p>';
                return;
            }

            for (const [monthYear, sales] of Object.entries(groupedData)) {
                const monthSection = document.createElement('div');
                monthSection.className = 'month-section';

                const monthHeader = document.createElement('div');
                monthHeader.className = 'month-header';
                monthHeader.textContent = monthYear;
                monthSection.appendChild(monthHeader);

                // Create table container for horizontal scrolling
                const tableContainer = document.createElement('div');
                tableContainer.className = 'table-container';

                const table = document.createElement('table');
                const thead = document.createElement('thead');
                thead.innerHTML = `
                    <tr>
                        <th>Reference Number</th>
                        <th>Date</th>
                        <th>Client Name</th>
                        <th>Billing Address</th>
                        <th>Billing Email</th>
                        <th>Billing Phone</th>
                        <th>Salesperson</th>
                        <th>Company</th>
                        <th>Item Purchase</th>
                        <th>Room</th>
                        <th>Quantity</th>
                        <th>Unit Cost (R)</th>
                        <th>Unit Price (R)</th>
                        <th>Profit/Unit (R)</th>
                        <th>Tax per Unit (R)</th>
                        <th>Payment Method</th>
                        <th>Total (R)</th>
                        <th>Total Profit (R)</th>
                    </tr>
                `;
                table.appendChild(thead);

                const tbody = document.createElement('tbody');
                let monthTotal = 0;
                let monthTotalTax = 0;
                let monthTotalProfit = 0;

                sales.forEach(sale => {
                    sale.items.forEach(item => {
                        const row = document.createElement('tr');

                        // Get basic values from the item
                        const quantity = parseFloat(item.quantity) || 0;
                        const taxPerProduct = parseFloat(item.tax_per_product) || 0;
                        const totalPrice = parseFloat(item.total_price) || 0;
                        const unitCost = parseFloat(item.unit_cost) || 0;

                        // Use the actual unit price from the sale (negotiated/discounted price)
                        const unitPrice = parseFloat(item.unit_price_excluding_tax) || (quantity > 0 ? totalPrice / quantity : 0);

                        // Calculate profit entirely on the frontend
                        const profitPerUnit = unitPrice - unitCost;
                        const totalProfit = profitPerUnit * quantity;

                        // Fetch room information for this item
                        const roomInfo = item.item_code ? `${item.room_name || 'N/A'}` : 'N/A';

                        row.innerHTML = `
                            <td>${sanitizeHTML(sale.reference_number)}</td>
                            <td>${sanitizeHTML(formatDate(sale.date))}</td>
                            <td>${sanitizeHTML(sale.billing_name)}</td>
                            <td>${sanitizeHTML(sale.billing_address)}</td>
                            <td>${sanitizeHTML(sale.billing_email)}</td>
                            <td>${sanitizeHTML(sale.billing_phone || 'N/A')}</td>
                            <td>${sanitizeHTML(sale.salesperson_name || 'N/A')}</td>
                            <td>${sanitizeHTML(sale.company_name || 'Shans Accessories PTY LTD')}</td>
                            <td>${sanitizeHTML(item.item_name)}</td>
                            <td>${sanitizeHTML(item.room_name || 'N/A')}</td>
                            <td>${quantity.toLocaleString()}</td>
                            <td>R ${Math.round(unitCost).toLocaleString()}</td>
                            <td>R ${Math.round(unitPrice).toLocaleString()}</td>
                            <td style="color: #28a745; font-weight: bold;">R ${Math.round(profitPerUnit).toLocaleString()}</td>
                            <td>R ${Math.round(taxPerProduct).toLocaleString()}</td>
                            <td>${sanitizeHTML(sale.payment_method)}</td>
                            <td>R ${Math.round(totalPrice).toLocaleString()}</td>
                            <td style="color: #28a745; font-weight: bold;">R ${Math.round(totalProfit).toLocaleString()}</td>
                        `;
                        tbody.appendChild(row);

                        monthTotal += totalPrice;
                        monthTotalTax += taxPerProduct * quantity;
                        monthTotalProfit += totalProfit; // Use the frontend-calculated profit
                    });
                });

                table.appendChild(tbody);

                // Add table to table container
                tableContainer.appendChild(table);
                monthSection.appendChild(tableContainer);

                // Create a summary section outside the table
                const summarySection = document.createElement('div');
                summarySection.className = 'month-summary';
                summarySection.innerHTML = `
                    <div class="summary-item">
                        <span class="summary-label">Total Tax for ${monthYear}:</span>
                        <span class="summary-value">R ${Math.round(monthTotalTax).toLocaleString()}</span>
                    </div>
                    <div class="summary-item profit-total">
                        <span class="summary-label">Total Profit for ${monthYear}:</span>
                        <span class="summary-value">R ${Math.round(monthTotalProfit).toLocaleString()}</span>
                    </div>
                    <div class="summary-item grand-total">
                        <span class="summary-label">Grand Total for ${monthYear}:</span>
                        <span class="summary-value">R ${Math.round(monthTotal).toLocaleString()}</span>
                    </div>
                `;
                monthSection.appendChild(summarySection);
                tablesContainer.appendChild(monthSection);
            }
        }

        function renderCards(data) {
            cardView.innerHTML = '';
            const groupedByMonth = {};

            data.forEach(sale => {
                const date = new Date(sale.date);
                const monthYear = date.toLocaleString('default', { month: 'long', year: 'numeric' });
                if (!groupedByMonth[monthYear]) {
                    groupedByMonth[monthYear] = [];
                }
                groupedByMonth[monthYear].push(sale);
            });

            for (const [monthYear, sales] of Object.entries(groupedByMonth)) {
                const monthHeader = document.createElement('div');
                monthHeader.className = 'month-header';
                monthHeader.textContent = monthYear;
                cardView.appendChild(monthHeader);

                sales.forEach(sale => {
                    sale.items.forEach(item => {
                        const quantity = parseFloat(item.quantity) || 0;
                        const taxPerProduct = parseFloat(item.tax_per_product) || 0;
                        const totalPrice = parseFloat(item.total_price) || 0;

                        // Use the actual unit price from the sale (negotiated/discounted price)
                        const unitPrice = parseFloat(item.unit_price_excluding_tax) || (quantity > 0 ? totalPrice / quantity : 0);

                        const card = document.createElement('div');
                        card.className = 'card';

                        // Get the unit cost from the product data
                        const unitCost = parseFloat(item.unit_cost) || 0;

                        // Calculate profit entirely on the frontend
                        const profitPerUnit = unitPrice - unitCost;
                        const totalProfit = profitPerUnit * quantity;

                        card.innerHTML = `
                            <h3>${sanitizeHTML(formatDate(sale.date))} - ${sanitizeHTML(sale.reference_number)}</h3>
                            <p><strong>Client Name:</strong> <span>${sanitizeHTML(sale.billing_name)}</span></p>
                            <p><strong>Billing Address:</strong> <span>${sanitizeHTML(sale.billing_address)}</span></p>
                            <p><strong>Billing Email:</strong> <span>${sanitizeHTML(sale.billing_email)}</span></p>
                            <p><strong>Billing Phone:</strong> <span>${sanitizeHTML(sale.billing_phone || 'N/A')}</span></p>
                            <p><strong>Salesperson:</strong> <span>${sanitizeHTML(sale.salesperson_name || 'N/A')}</span></p>
                            <p><strong>Company:</strong> <span>${sanitizeHTML(sale.company_name || 'Shans Accessories PTY LTD')}</span></p>
                            <p><strong>Item:</strong> <span>${sanitizeHTML(item.item_name)}</span></p>
                            <p><strong>Room:</strong> <span>${sanitizeHTML(item.room_name || 'N/A')}</span></p>
                            <p><strong>Quantity:</strong> <span>${quantity.toLocaleString()}</span></p>
                            <p><strong>Unit Price:</strong> <span>R ${Math.round(unitPrice).toLocaleString()}</span></p>
                            <p><strong>Unit Cost:</strong> <span>R ${Math.round(unitCost).toLocaleString()}</span></p>
                            <p><strong>Profit per Unit:</strong> <span style="color: #28a745;">R ${Math.round(profitPerUnit).toLocaleString()}</span></p>
                            <p><strong>Tax per Unit:</strong> <span>R ${Math.round(taxPerProduct).toLocaleString()}</span></p>
                            <p><strong>Total Value:</strong> <span>R ${Math.round(unitPrice * quantity).toLocaleString()}</span></p>
                            <p><strong>Total Cost:</strong> <span>R ${Math.round(unitCost * quantity).toLocaleString()}</span></p>
                            <p><strong>Total Profit:</strong> <span style="color: #28a745;">R ${Math.round(totalProfit).toLocaleString()}</span></p>
                            <p><strong>Total Tax:</strong> <span>R ${Math.round(taxPerProduct * quantity).toLocaleString()}</span></p>
                            <p><strong>Payment Method:</strong> <span>${sanitizeHTML(sale.payment_method)}</span></p>
                            <p><strong>Total Price:</strong> <span style="font-weight: bold; color: #0066cc;">R ${Math.round(totalPrice).toLocaleString()}</span></p>
                        `;
                        cardView.appendChild(card);
                    });
                });

                const monthlyTotal = sales.reduce((total, sale) => {
                    return total + sale.items.reduce((itemTotal, item) => {
                        return itemTotal + (parseFloat(item.total_price) || 0);
                    }, 0);
                }, 0);

                const monthlyTaxTotal = sales.reduce((total, sale) => {
                    return total + sale.items.reduce((itemTotal, item) => {
                        const quantity = parseFloat(item.quantity) || 0;
                        const taxPerProduct = parseFloat(item.tax_per_product) || 0;
                        return itemTotal + (quantity * taxPerProduct);
                    }, 0);
                }, 0);

                const monthlyProfitTotal = sales.reduce((total, sale) => {
                    return total + sale.items.reduce((itemTotal, item) => {
                        // Calculate profit on the frontend for each item
                        const quantity = parseFloat(item.quantity) || 0;
                        const unitPrice = parseFloat(item.unit_price_excluding_tax) || 0;
                        const unitCost = parseFloat(item.unit_cost) || 0;
                        const profitPerUnit = unitPrice - unitCost;
                        const itemProfit = profitPerUnit * quantity;
                        return itemTotal + itemProfit;
                    }, 0);
                }, 0);

                const totalCard = document.createElement('div');
                totalCard.className = 'card';
                totalCard.innerHTML = `
                    <h3>Monthly Totals for ${monthYear}</h3>
                    <p><strong>Total Tax:</strong> <span>R ${Math.round(monthlyTaxTotal).toLocaleString()}</span></p>
                    <p style="padding: 8px; background-color: #e6ffe6; border-left: 4px solid #28a745; border-radius: 4px; margin: 10px 0;">
                        <strong>Total Profit:</strong>
                        <span style="font-weight: bold; color: #28a745; font-size: 16px;">R ${Math.round(monthlyProfitTotal).toLocaleString()}</span>
                    </p>
                    <p><strong>Total Sales:</strong> <span style="font-weight: bold; color: #0066cc; font-size: 16px;">R ${Math.round(monthlyTotal).toLocaleString()}</span></p>
                `;
                totalCard.style.backgroundColor = '#f8f9fa';
                cardView.appendChild(totalCard);
            }
        }

        function saveToExcel() {
            if (allSalesData.length === 0) {
                alert('No data available to save!');
                return;
            }

            const flattenedData = allSalesData.flatMap(sale =>
                sale.items.map(item => {
                    const quantity = parseFloat(item.quantity) || 0;
                    const totalPrice = parseFloat(item.total_price) || 0;

                    // Use the actual unit price from the sale (negotiated/discounted price)
                    const unitPrice = parseFloat(item.unit_price_excluding_tax) || (quantity > 0 ? totalPrice / quantity : 0);

                    // Get the unit cost from the product data
                    const unitCost = parseFloat(item.unit_cost) || 0;

                    // Calculate profit entirely on the frontend
                    const profitPerUnit = unitPrice - unitCost;
                    const totalProfit = profitPerUnit * quantity;

                    // Calculate total values
                    const totalRetailValue = unitPrice * quantity;
                    const totalCost = unitCost * quantity;

                    return {
                        "Reference Number": sale.reference_number,
                        "Date": formatDate(sale.date),
                        "Client Name": sale.billing_name,
                        "Billing Address": sale.billing_address,
                        "Billing Email": sale.billing_email,
                        "Billing Phone": sale.billing_phone || 'N/A',
                        "Salesperson": sale.salesperson_name || 'N/A',
                        "Company": sale.company_name || 'Shans Accessories PTY LTD',
                        "Item Purchased": item.item_name,
                        "Room": item.room_name || 'N/A',
                        "Quantity": quantity,
                        "Unit Price (R)": Math.round(unitPrice),
                        "Unit Cost (R)": Math.round(unitCost),
                        "Profit per Unit (R)": Math.round(profitPerUnit),
                        "Tax per Unit (R)": Math.round(parseFloat(item.tax_per_product) || 0),
                        "Total Value (R)": Math.round(totalRetailValue),
                        "Total Cost (R)": Math.round(totalCost),
                        "Total Profit (R)": Math.round(totalProfit),
                        "Total Tax (R)": Math.round(parseFloat(item.tax_per_product || 0) * quantity),
                        "Payment Method": sale.payment_method,
                        "Total Price (R)": Math.round(totalPrice)
                    };
                })
            );

            const worksheet = XLSX.utils.json_to_sheet(flattenedData);
            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, 'Sales Data');
            XLSX.writeFile(workbook, `Sales_Data_${new Date().toISOString().split('T')[0]}.xlsx`);
        }

        function toggleView() {
            const tables = document.getElementById('tables-container');
            if (tables.style.display === 'none') {
                tables.style.display = 'block';
                cardView.style.display = 'none';
            } else {
                tables.style.display = 'none';
                cardView.style.display = 'grid';
            }
        }

        function printSalesData() {
            // Make sure we're in table view before printing
            if (tablesContainer.style.display === 'none') {
                tablesContainer.style.display = 'block';
                cardView.style.display = 'none';
            }

            // Print the page
            window.print();
        }

        function filterAndRender(query) {
            const filteredData = allSalesData.filter(sale =>
                sale.reference_number.toLowerCase().includes(query.toLowerCase()) ||
                sale.date.toLowerCase().includes(query.toLowerCase()) ||
                sale.billing_name.toLowerCase().includes(query.toLowerCase()) ||
                sale.billing_address.toLowerCase().includes(query.toLowerCase()) ||
                sale.billing_email.toLowerCase().includes(query.toLowerCase()) ||
                (sale.billing_phone && sale.billing_phone.toLowerCase().includes(query.toLowerCase())) ||
                (sale.salesperson_name && sale.salesperson_name.toLowerCase().includes(query.toLowerCase())) ||
                (sale.company_name && sale.company_name.toLowerCase().includes(query.toLowerCase())) ||
                sale.payment_method.toLowerCase().includes(query.toLowerCase()) ||
                sale.items.some(item =>
                    item.item_name.toLowerCase().includes(query.toLowerCase()) ||
                    (item.room_name && item.room_name.toLowerCase().includes(query.toLowerCase())) ||
                    String(item.quantity).includes(query) ||
                    String(item.tax_per_product).includes(query) ||
                    String(item.total_price).includes(query)
                )
            );

            groupDataByMonth(filteredData);
            renderTables(groupedSalesData);
            renderCards(filteredData);
        }

        function sanitizeHTML(str) {
            if (str === null || str === undefined) return '';
            const temp = document.createElement('div');
            temp.textContent = str;
            return temp.innerHTML;
        }

        function formatDate(dateString) {
            if (!dateString) return '';
            const options = { day: 'numeric', month: 'long', year: 'numeric' };
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', options);
        }

        function debounce(func, delay) {
            let debounceTimer;
            return function() {
                const context = this;
                const args = arguments;
                clearTimeout(debounceTimer);
                debounceTimer = setTimeout(() => func.apply(context, args), delay);
            }
        }

        viewToggle.addEventListener('click', toggleView);
        searchBar.addEventListener('input', debounce((e) => {
            const query = e.target.value.trim();
            filterAndRender(query);
        }, 300));

        document.addEventListener('DOMContentLoaded', fetchSalesData);
    </script>
</body>
</html>