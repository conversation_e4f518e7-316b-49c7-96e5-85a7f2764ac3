// database.js
const { Pool } = require('pg');
require('dotenv').config();

// Create PostgreSQL connection pool
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});

console.log('PostgreSQL database pool initialized successfully');

// PostgreSQL pool already has the query method and connect method built-in
// No need for custom executeQuery function - PostgreSQL handles everything natively

// Initialize database tables with retry logic
async function initializeTables(retryCount = 5, delay = 5000) {
  let attempts = 0;

  while (attempts < retryCount) {
    let client;
    let clientReleased = false;

    try {
      console.log(`Attempting to connect to database (attempt ${attempts + 1}/${retryCount})...`);
      client = await pool.connect();

      // Execute table creation queries for PostgreSQL
      await client.query(`
        CREATE TABLE IF NOT EXISTS rooms (
          id SERIAL PRIMARY KEY,
          name VARCHAR(255) NOT NULL,
          color VARCHAR(50) NOT NULL,
          location VARCHAR(255) NOT NULL
        )
      `);

      await client.query(`
        CREATE TABLE IF NOT EXISTS products (
          item_code VARCHAR(100) PRIMARY KEY,
          room_id INTEGER NOT NULL,
          item_name VARCHAR(255) NOT NULL,
          car_brand VARCHAR(100) NOT NULL,
          car_model VARCHAR(100) NOT NULL,
          unit_retail_price DECIMAL(10,2) NOT NULL,
          wholesale_price DECIMAL(10,2) NOT NULL,
          unit_cost DECIMAL(10,2) NOT NULL,
          supplier_code VARCHAR(100) NOT NULL,
          available_stock INTEGER NOT NULL,
          location VARCHAR(255) NOT NULL,
          colour_tape VARCHAR(50) NOT NULL,
          profit DECIMAL(10,2) NOT NULL,
          additional_comments TEXT,
          product_category VARCHAR(100) NOT NULL,
          FOREIGN KEY(room_id) REFERENCES rooms(id) ON DELETE CASCADE
        )
      `);

      await client.query(`
        CREATE TABLE IF NOT EXISTS sales (
          id SERIAL PRIMARY KEY,
          reference_number VARCHAR(100) NOT NULL UNIQUE,
          date VARCHAR(50) NOT NULL,
          billing_name VARCHAR(255) NOT NULL,
          billing_address TEXT NOT NULL,
          billing_email VARCHAR(255) NOT NULL,
          billing_phone VARCHAR(50),
          shipping_name VARCHAR(255),
          shipping_address TEXT,
          shipping_email VARCHAR(255),
          shipping_phone VARCHAR(50),
          payment_method VARCHAR(100),
          subtotal DECIMAL(10,2) NOT NULL,
          tax DECIMAL(10,2) NOT NULL,
          total DECIMAL(10,2) NOT NULL,
          total_profit DECIMAL(10,2),
          salesperson_name VARCHAR(255),
          company_name VARCHAR(255),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);

      await client.query(`
        CREATE TABLE IF NOT EXISTS sale_items (
          id SERIAL PRIMARY KEY,
          sale_id INTEGER NOT NULL,
          item_code VARCHAR(100) NOT NULL,
          item_name VARCHAR(255) NOT NULL,
          quantity INTEGER NOT NULL,
          unit_price_excluding_tax DECIMAL(10,2) NOT NULL,
          unit_cost DECIMAL(10,2),
          profit_per_unit DECIMAL(10,2),
          total_profit DECIMAL(10,2),
          tax_per_product DECIMAL(10,2) NOT NULL,
          total_price DECIMAL(10,2) NOT NULL,
          FOREIGN KEY(sale_id) REFERENCES sales(id)
        )
      `);

      await client.query(`
        CREATE TABLE IF NOT EXISTS users (
          id SERIAL PRIMARY KEY,
          email VARCHAR(255) UNIQUE NOT NULL,
          password_hash VARCHAR(255) NOT NULL,
          is_admin BOOLEAN DEFAULT FALSE,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);

      await client.query(`
        CREATE TABLE IF NOT EXISTS categories (
          id SERIAL PRIMARY KEY,
          category_id VARCHAR(100) NOT NULL UNIQUE,
          name VARCHAR(255) NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);

      await client.query(`
        CREATE TABLE IF NOT EXISTS car_brands (
          id SERIAL PRIMARY KEY,
          brand_id VARCHAR(100) NOT NULL UNIQUE,
          name VARCHAR(255) NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);

      await client.query(`
        CREATE TABLE IF NOT EXISTS car_models (
          id SERIAL PRIMARY KEY,
          model_id VARCHAR(100) NOT NULL UNIQUE,
          name VARCHAR(255) NOT NULL,
          brand_id VARCHAR(100) NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Mark that we're about to release the client
      clientReleased = true;
      client.release();

      console.log('Database tables initialized successfully');
      return; // Success, exit the function
    } catch (error) {
      attempts++;
      console.error(`Error initializing database tables (attempt ${attempts}/${retryCount}):`, error);

      if (attempts >= retryCount) {
        console.error('Maximum retry attempts reached. Could not initialize database tables.');
        throw error;
      }

      console.log(`Retrying in ${delay/1000} seconds...`);
      await new Promise(resolve => setTimeout(resolve, delay));
    } finally {
      // Only release the client if it exists and hasn't been released yet
      if (client && !clientReleased) {
        try {
          client.release();
        } catch (releaseErr) {
          console.error('Error releasing client:', releaseErr.message);
        }
      }
    }
  }
}

// Initialize tables when the application starts with a more robust approach
(async () => {
  try {
    await initializeTables();
    console.log('Database initialization completed successfully');
  } catch (error) {
    console.error('Failed to initialize database after multiple attempts:', error);
    // The application can still run even if table initialization fails
    // as the tables might already exist from previous runs
    console.log('Continuing application startup despite database initialization failure');
  }
})();

// Test database connection with retry logic
async function testDatabaseConnection(retryCount = 5, delay = 1000) {
  let attempts = 0;

  while (attempts < retryCount) {
    let client;
    let clientReleased = false;

    try {
      console.log(`Testing database connection (attempt ${attempts + 1}/${retryCount})...`);
      client = await pool.connect();

      const result = await client.query('SELECT NOW() as now');
      console.log('Database connected successfully at:', result.rows[0].now);

      // Mark that we're about to release the client
      clientReleased = true;
      client.release();

      return true;
    } catch (err) {
      attempts++;
      console.error(`Error connecting to database (attempt ${attempts}/${retryCount}):`, err.message);

      if (attempts >= retryCount) {
        console.error('Maximum retry attempts reached. Could not connect to database.');
        return false;
      }

      console.log(`Retrying connection in ${delay/1000} seconds...`);
      await new Promise(resolve => setTimeout(resolve, delay));
    } finally {
      // Only release the client if it exists and hasn't been released yet
      if (client && !clientReleased) {
        try {
          client.release();
        } catch (releaseErr) {
          console.error('Error releasing client:', releaseErr.message);
        }
      }
    }
  }
  return false;
}

// Execute the test connection
testDatabaseConnection().then(success => {
  if (!success) {
    console.warn('WARNING: Database connection test failed, but application will continue to run.');
    console.warn('Some database operations may fail until connection is restored.');
  }
});

module.exports = pool;
module.exports.initializeTables = initializeTables;