// database.js
const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

// Ensure data directory exists
const dataDir = path.join(__dirname, 'data');
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

// Create SQLite database connection
const dbPath = path.join(dataDir, 'inventory.db');
console.log('SQLite database path:', dbPath);

const db = new Database(dbPath);

// Enable foreign keys
db.pragma('foreign_keys = ON');

console.log('SQLite database initialized successfully');

// Helper function to execute queries
const executeQuery = (sql, params = []) => {
  try {
    // Convert PostgreSQL-style parameters ($1, $2) to SQLite-style (?)
    let sqliteQuery = sql;
    let sqliteParams = params;

    // Replace $1, $2, etc. with ?
    if (sql.includes('$')) {
      sqliteQuery = sql.replace(/\$(\d+)/g, '?');
    }

    // Handle RETURNING clause for SQLite
    if (sqliteQuery.includes('RETURNING')) {
      const parts = sqliteQuery.split('RETURNING');
      const mainSql = parts[0].trim();
      const returningFields = parts[1].trim();

      if (mainSql.toUpperCase().startsWith('INSERT')) {
        const stmt = db.prepare(mainSql);
        const result = stmt.run(sqliteParams);

        if (result.changes > 0) {
          // Determine the table name from the INSERT statement
          const tableMatch = mainSql.match(/INSERT\s+INTO\s+(\w+)/i);
          const tableName = tableMatch ? tableMatch[1] : 'products';

          // Get the inserted row
          const selectSql = `SELECT ${returningFields} FROM ${tableName} WHERE rowid = ?`;
          const selectStmt = db.prepare(selectSql);
          const rows = selectStmt.all([result.lastInsertRowid]);
          return Promise.resolve({ rows });
        }
        return Promise.resolve({ rows: [] });
      } else if (mainSql.toUpperCase().startsWith('UPDATE')) {
        const stmt = db.prepare(mainSql);
        const result = stmt.run(sqliteParams);

        if (result.changes > 0) {
          // Determine the table name from the UPDATE statement
          const tableMatch = mainSql.match(/UPDATE\s+(\w+)/i);
          const tableName = tableMatch ? tableMatch[1] : 'products';

          // For UPDATE, we need to identify the record - usually the last param is the ID
          const id = sqliteParams[sqliteParams.length - 1];
          const selectSql = `SELECT ${returningFields} FROM ${tableName} WHERE item_code = ? OR id = ?`;
          const selectStmt = db.prepare(selectSql);
          const rows = selectStmt.all([id, id]);
          return Promise.resolve({ rows });
        }
        return Promise.resolve({ rows: [] });
      } else if (mainSql.toUpperCase().startsWith('DELETE')) {
        const stmt = db.prepare(mainSql);
        const result = stmt.run(sqliteParams);
        return Promise.resolve({ rows: [], rowCount: result.changes });
      }
    }

    if (sqliteQuery.trim().toUpperCase().startsWith('SELECT')) {
      const stmt = db.prepare(sqliteQuery);
      const rows = stmt.all(sqliteParams);
      return Promise.resolve({ rows });
    } else {
      const stmt = db.prepare(sqliteQuery);
      const result = stmt.run(sqliteParams);
      return Promise.resolve({
        rows: [],
        rowCount: result.changes
      });
    }
  } catch (error) {
    return Promise.reject(error);
  }
};

// Create a pool-like interface for compatibility
const pool = {
  // Direct query method for easier use
  query: executeQuery,

  // Connect method that returns a client with query method
  connect: () => Promise.resolve({
    query: executeQuery,
    release: () => {} // No-op for SQLite
  })
};

// Initialize database tables with retry logic
async function initializeTables(retryCount = 5, delay = 5000) {
  let attempts = 0;

  while (attempts < retryCount) {
    let client;
    let clientReleased = false;

    try {
      console.log(`Attempting to connect to database (attempt ${attempts + 1}/${retryCount})...`);
      client = await pool.connect();

      // Execute table creation queries for SQLite
      await client.query(`
        CREATE TABLE IF NOT EXISTS rooms (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          color TEXT NOT NULL,
          location TEXT NOT NULL
        )
      `);

      await client.query(`
        CREATE TABLE IF NOT EXISTS products (
          item_code TEXT PRIMARY KEY,
          room_id INTEGER NOT NULL,
          item_name TEXT NOT NULL,
          car_brand TEXT NOT NULL,
          car_model TEXT NOT NULL,
          unit_retail_price REAL NOT NULL,
          wholesale_price REAL NOT NULL,
          unit_cost REAL NOT NULL,
          supplier_code TEXT NOT NULL,
          available_stock INTEGER NOT NULL,
          location TEXT NOT NULL,
          colour_tape TEXT NOT NULL,
          profit REAL NOT NULL,
          additional_comments TEXT,
          product_category TEXT NOT NULL,
          FOREIGN KEY(room_id) REFERENCES rooms(id) ON DELETE CASCADE
        )
      `);

      await client.query(`
        CREATE TABLE IF NOT EXISTS sales (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          reference_number TEXT NOT NULL UNIQUE,
          date TEXT NOT NULL,
          billing_name TEXT NOT NULL,
          billing_address TEXT NOT NULL,
          billing_email TEXT NOT NULL,
          billing_phone TEXT,
          shipping_name TEXT,
          shipping_address TEXT,
          shipping_email TEXT,
          shipping_phone TEXT,
          payment_method TEXT,
          subtotal REAL NOT NULL,
          tax REAL NOT NULL,
          total REAL NOT NULL,
          total_profit REAL,
          salesperson_name TEXT,
          company_name TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      await client.query(`
        CREATE TABLE IF NOT EXISTS sale_items (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          sale_id INTEGER NOT NULL,
          item_code TEXT NOT NULL,
          item_name TEXT NOT NULL,
          quantity INTEGER NOT NULL,
          unit_price_excluding_tax REAL NOT NULL,
          unit_cost REAL,
          profit_per_unit REAL,
          total_profit REAL,
          tax_per_product REAL NOT NULL,
          total_price REAL NOT NULL,
          FOREIGN KEY(sale_id) REFERENCES sales(id)
        )
      `);

      // Mark that we're about to release the client
      clientReleased = true;
      client.release();

      console.log('Database tables initialized successfully');
      return; // Success, exit the function
    } catch (error) {
      attempts++;
      console.error(`Error initializing database tables (attempt ${attempts}/${retryCount}):`, error);

      if (attempts >= retryCount) {
        console.error('Maximum retry attempts reached. Could not initialize database tables.');
        throw error;
      }

      console.log(`Retrying in ${delay/1000} seconds...`);
      await new Promise(resolve => setTimeout(resolve, delay));
    } finally {
      // Only release the client if it exists and hasn't been released yet
      if (client && !clientReleased) {
        try {
          client.release();
        } catch (releaseErr) {
          console.error('Error releasing client:', releaseErr.message);
        }
      }
    }
  }
}

// Initialize tables when the application starts with a more robust approach
(async () => {
  try {
    await initializeTables();
    console.log('Database initialization completed successfully');
  } catch (error) {
    console.error('Failed to initialize database after multiple attempts:', error);
    // The application can still run even if table initialization fails
    // as the tables might already exist from previous runs
    console.log('Continuing application startup despite database initialization failure');
  }
})();

// Test database connection with retry logic
async function testDatabaseConnection(retryCount = 5, delay = 1000) {
  let attempts = 0;

  while (attempts < retryCount) {
    let client;
    let clientReleased = false;

    try {
      console.log(`Testing database connection (attempt ${attempts + 1}/${retryCount})...`);
      client = await pool.connect();

      const result = await client.query('SELECT datetime(\'now\') as now');
      console.log('Database connected successfully at:', result.rows[0].now);

      // Mark that we're about to release the client
      clientReleased = true;
      client.release();

      return true;
    } catch (err) {
      attempts++;
      console.error(`Error connecting to database (attempt ${attempts}/${retryCount}):`, err.message);

      if (attempts >= retryCount) {
        console.error('Maximum retry attempts reached. Could not connect to database.');
        return false;
      }

      console.log(`Retrying connection in ${delay/1000} seconds...`);
      await new Promise(resolve => setTimeout(resolve, delay));
    } finally {
      // Only release the client if it exists and hasn't been released yet
      if (client && !clientReleased) {
        try {
          client.release();
        } catch (releaseErr) {
          console.error('Error releasing client:', releaseErr.message);
        }
      }
    }
  }
  return false;
}

// Execute the test connection
testDatabaseConnection().then(success => {
  if (!success) {
    console.warn('WARNING: Database connection test failed, but application will continue to run.');
    console.warn('Some database operations may fail until connection is restored.');
  }
});

module.exports = pool;
module.exports.initializeTables = initializeTables;